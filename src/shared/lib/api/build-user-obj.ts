import { getJWTPayloadProperty } from '../jwt-tools/get-jwt-payload-property.lib';

export interface UserObject {
	name: string;
	socialName: string | null;
	document: string;
	time: string;
	system: number;
	loginId: number;
	roles: number[];
	email: string;
	additionalData: {
		nome: string;
		email: string;
		usuario: string;
		setor: string;
		cargo: string;
		unidade: string;
		empresa: number;
		matricula: number;
	};
	issuedAt: number;
	expiresAt: number;
}

export function buildUserObject(token: string): UserObject {
	const name = getJWTPayloadProperty<string>(token, 'nome') ?? '';
	const socialName = getJWTPayloadProperty<string | null>(token, 'nomeSocial');
	const document = getJWTPayloadProperty<string>(token, 'documento') ?? '';
	const time = getJWTPayloadProperty<string>(token, 'time') ?? '';
	const system = getJWTPayloadProperty<number>(token, 'sistema') ?? 0;
	const loginId = getJWTPayloadProperty<number>(token, 'loginId') ?? 0;
	const roles = getJWTPayloadProperty<number[]>(token, 'roles') ?? [];
	const additionalData = getJWTPayloadProperty<any>(token, 'dadosAdicionais') ?? {};
	const issuedAt = getJWTPayloadProperty<number>(token, 'iat') ?? 0;
	const expiresAt = getJWTPayloadProperty<number>(token, 'exp') ?? 0;

	return {
		name,
		socialName,
		document,
		time,
		system,
		loginId,
		roles,
		email: additionalData.EMAIL ?? '',
		additionalData: {
			nome: additionalData.NOME ?? '',
			email: additionalData.EMAIL ?? '',
			usuario: additionalData.USUARIO ?? '',
			setor: additionalData.SETOR ?? '',
			cargo: additionalData.CARGO ?? '',
			unidade: additionalData.UNIDADE ?? '',
			empresa: additionalData.EMPRESA ?? 0,
			matricula: additionalData.MATRICULA ?? 0,
		},
		issuedAt,
		expiresAt,
	};
}
